import React from 'react';

const PricingCard: React.FC<{
    name: string;
    price: string;
    period: string;
    description: string;
    features: string[];
    isPopular?: boolean;
    buttonText: string;
    buttonLink: string;
}> = ({ name, price, period, description, features, isPopular, buttonText, buttonLink }) => (
    <div className={`relative bg-white rounded-2xl shadow-lg border-2 p-8 ${isPopular ? 'border-julep-green scale-105' : 'border-slate-200'}`}>
        {isPopular && (
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-julep-green text-white px-4 py-2 rounded-full text-sm font-medium">
                    Most Popular
                </span>
            </div>
        )}
        
        <div className="text-center">
            <h3 className="text-2xl font-bold text-julep-dark">{name}</h3>
            <p className="mt-2 text-slate-600">{description}</p>
            
            <div className="mt-6">
                <span className="text-4xl font-bold text-julep-dark">{price}</span>
                <span className="text-slate-600">/{period}</span>
            </div>
        </div>
        
        <ul className="mt-8 space-y-4">
            {features.map((feature, index) => (
                <li key={index} className="flex items-center gap-3">
                    <svg className="w-5 h-5 text-julep-green flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-slate-700">{feature}</span>
                </li>
            ))}
        </ul>
        
        <div className="mt-8">
            <a 
                href={buttonLink} 
                className={`w-full block text-center font-bold px-6 py-3 rounded-lg transition-all ${
                    isPopular 
                        ? 'bg-julep-green text-white hover:bg-opacity-90 shadow-lg' 
                        : 'bg-slate-100 text-julep-dark hover:bg-slate-200'
                }`}
            >
                {buttonText}
            </a>
        </div>
    </div>
);

const Pricing: React.FC = () => {
    return (
        <section className="py-20 bg-white">
            <div className="container mx-auto px-6">
                <div className="text-center max-w-3xl mx-auto">
                    <h2 className="text-3xl md:text-4xl font-bold text-julep-dark">
                        Simple, Transparent Pricing
                    </h2>
                    <p className="mt-4 text-lg text-julep-gray">
                        Choose the plan that fits your organization. All plans include free setup, migration, and 24/7 support.
                    </p>
                </div>
                
                <div className="mt-16 grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                    <PricingCard
                        name="Starter"
                        price="$49"
                        period="month"
                        description="Perfect for small nonprofits getting started"
                        features={[
                            "Up to 1,000 contacts",
                            "Unlimited donation pages",
                            "Email marketing",
                            "Basic reporting",
                            "Phone & email support"
                        ]}
                        buttonText="Start Free Trial"
                        buttonLink="#"
                    />
                    
                    <PricingCard
                        name="Professional"
                        price="$99"
                        period="month"
                        description="For growing organizations with advanced needs"
                        features={[
                            "Up to 5,000 contacts",
                            "Advanced automation",
                            "Custom donation forms",
                            "Advanced reporting & analytics",
                            "Priority support",
                            "Integrations & API access"
                        ]}
                        isPopular={true}
                        buttonText="Start Free Trial"
                        buttonLink="#"
                    />
                    
                    <PricingCard
                        name="Enterprise"
                        price="$199"
                        period="month"
                        description="For large organizations with complex requirements"
                        features={[
                            "Unlimited contacts",
                            "White-label options",
                            "Custom integrations",
                            "Dedicated account manager",
                            "Advanced security features",
                            "Custom training & onboarding"
                        ]}
                        buttonText="Contact Sales"
                        buttonLink="#"
                    />
                </div>
                
                {/* FAQ Section */}
                <div className="mt-20 max-w-3xl mx-auto">
                    <h3 className="text-2xl font-bold text-julep-dark text-center mb-8">
                        Frequently Asked Questions
                    </h3>
                    
                    <div className="space-y-6">
                        <div className="bg-slate-50 rounded-lg p-6">
                            <h4 className="font-semibold text-julep-dark mb-2">Is there a setup fee?</h4>
                            <p className="text-slate-600">No setup fees ever. We include free migration from your current system and onboarding support.</p>
                        </div>
                        
                        <div className="bg-slate-50 rounded-lg p-6">
                            <h4 className="font-semibold text-julep-dark mb-2">Can I cancel anytime?</h4>
                            <p className="text-slate-600">Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees.</p>
                        </div>
                        
                        <div className="bg-slate-50 rounded-lg p-6">
                            <h4 className="font-semibold text-julep-dark mb-2">What payment methods do you accept?</h4>
                            <p className="text-slate-600">We accept all major credit cards and ACH transfers. Annual plans receive a 20% discount.</p>
                        </div>
                    </div>
                </div>
                
                {/* Money Back Guarantee */}
                <div className="mt-16 text-center">
                    <div className="inline-flex items-center gap-3 bg-julep-green/10 text-julep-green px-6 py-4 rounded-lg">
                        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <div>
                            <div className="font-semibold">30-Day Money-Back Guarantee</div>
                            <div className="text-sm opacity-80">Not satisfied? Get a full refund, no questions asked.</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Pricing;
