import React, { useState, useEffect } from 'react';

const ConversionOptimization: React.FC = () => {
    const [showExitIntent, setShowExitIntent] = useState(false);
    const [showChatWidget, setShowChatWidget] = useState(false);

    useEffect(() => {
        // Show chat widget after 30 seconds
        const chatTimer = setTimeout(() => {
            setShowChatWidget(true);
        }, 30000);

        // Exit intent detection
        const handleMouseLeave = (e: MouseEvent) => {
            if (e.clientY <= 0 && !showExitIntent) {
                setShowExitIntent(true);
            }
        };

        document.addEventListener('mouseleave', handleMouseLeave);

        return () => {
            clearTimeout(chatTimer);
            document.removeEventListener('mouseleave', handleMouseLeave);
        };
    }, [showExitIntent]);

    return (
        <>
            {/* Exit Intent Popup */}
            {showExitIntent && (
                <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-2xl p-8 max-w-md w-full relative animate-fadeInUp">
                        <button
                            onClick={() => setShowExitIntent(false)}
                            className="absolute top-4 right-4 text-slate-400 hover:text-slate-600"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                        
                        <div className="text-center">
                            <div className="text-4xl mb-4">🎯</div>
                            <h3 className="text-2xl font-bold text-julep-dark mb-4">
                                Wait! Don't Miss Out
                            </h3>
                            <p className="text-slate-600 mb-6">
                                Join 500+ nonprofits raising 30% more money with Julep. Get your free demo before you go!
                            </p>
                            
                            <div className="space-y-3">
                                <a 
                                    href="#" 
                                    className="w-full bg-julep-green text-white font-bold px-6 py-3 rounded-lg hover:bg-opacity-90 transition-all shadow-lg block text-center"
                                    onClick={() => setShowExitIntent(false)}
                                >
                                    Get Free Demo Now
                                </a>
                                <button 
                                    onClick={() => setShowExitIntent(false)}
                                    className="w-full text-slate-500 hover:text-slate-700 text-sm"
                                >
                                    No thanks, I'll figure it out myself
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Chat Widget */}
            {showChatWidget && (
                <div className="fixed bottom-6 right-6 z-40">
                    <div className="bg-white rounded-2xl shadow-2xl border border-slate-200 p-6 max-w-sm animate-fadeInUp">
                        <div className="flex items-center gap-3 mb-4">
                            <div className="w-10 h-10 bg-julep-green rounded-full flex items-center justify-center text-white font-bold">
                                JC
                            </div>
                            <div>
                                <div className="font-semibold text-julep-dark">Julep Support</div>
                                <div className="text-sm text-slate-600">Typically replies in minutes</div>
                            </div>
                            <button
                                onClick={() => setShowChatWidget(false)}
                                className="ml-auto text-slate-400 hover:text-slate-600"
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        <p className="text-slate-700 mb-4">
                            👋 Hi! I'm here to help you find the perfect fundraising solution. Have any questions about Julep?
                        </p>
                        
                        <div className="space-y-2">
                            <button className="w-full text-left p-3 bg-slate-50 hover:bg-slate-100 rounded-lg text-sm transition-colors">
                                How does Julep compare to other CRMs?
                            </button>
                            <button className="w-full text-left p-3 bg-slate-50 hover:bg-slate-100 rounded-lg text-sm transition-colors">
                                Can you help me migrate my data?
                            </button>
                            <button className="w-full text-left p-3 bg-slate-50 hover:bg-slate-100 rounded-lg text-sm transition-colors">
                                What's included in the free trial?
                            </button>
                        </div>
                        
                        <div className="mt-4 pt-4 border-t border-slate-200">
                            <a 
                                href="#" 
                                className="w-full bg-julep-green text-white font-medium px-4 py-2 rounded-lg hover:bg-opacity-90 transition-all text-center block text-sm"
                            >
                                Start Chat
                            </a>
                        </div>
                    </div>
                </div>
            )}

            {/* Floating CTA Button */}
            <div className="fixed bottom-6 left-6 z-30">
                <a 
                    href="#" 
                    className="bg-julep-green text-white font-bold px-6 py-3 rounded-full shadow-2xl hover:bg-opacity-90 transition-all flex items-center gap-2 animate-pulse"
                >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                    <span>Free Demo</span>
                </a>
            </div>

            {/* Sticky Header CTA */}
            <div className="fixed top-0 left-0 right-0 bg-julep-green text-white py-2 px-4 z-20 transform -translate-y-full transition-transform duration-300" id="sticky-cta">
                <div className="container mx-auto flex items-center justify-between">
                    <div className="text-sm">
                        <span className="font-medium">Limited Time:</span> Free setup & migration (worth $2,500)
                    </div>
                    <a 
                        href="#" 
                        className="bg-white text-julep-green font-bold px-4 py-1 rounded text-sm hover:bg-opacity-90 transition-all"
                    >
                        Claim Offer
                    </a>
                </div>
            </div>

            {/* Progress Bar */}
            <div className="fixed top-0 left-0 right-0 h-1 bg-slate-200 z-10">
                <div 
                    className="h-full bg-julep-green transition-all duration-300 ease-out"
                    style={{ width: '0%' }}
                    id="progress-bar"
                ></div>
            </div>
        </>
    );
};

export default ConversionOptimization;
