
import React from 'react';

const CTA: React.FC = () => {
    return (
        <section className="bg-slate-50">
            <div className="container mx-auto px-6 py-20">
                <div className="bg-julep-dark rounded-2xl p-8 md:p-16 text-center shadow-xl -mb-40 relative z-10 overflow-hidden">
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-10">
                        <svg className="w-full h-full" viewBox="0 0 100 100" fill="none">
                            <defs>
                                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
                                </pattern>
                            </defs>
                            <rect width="100" height="100" fill="url(#grid)" />
                        </svg>
                    </div>

                    <div className="relative z-10">
                        {/* Urgency Badge */}
                        <div className="inline-flex items-center gap-2 bg-julep-green/20 text-julep-green px-4 py-2 rounded-full text-sm font-medium mb-6">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            Limited Time: Free Setup & Migration (Worth $2,500)
                        </div>

                        <h2 className="text-3xl md:text-4xl font-bold text-white">
                            Ready to Raise 30% More Money?
                        </h2>
                        <p className="mt-4 text-lg text-slate-300 max-w-2xl mx-auto">
                            Join 500+ nonprofits already using Julep. Schedule a personalized demo and see exactly how we'll help you streamline fundraising and grow donations.
                        </p>

                        {/* Value Props */}
                        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto text-sm">
                            <div className="flex items-center justify-center gap-2 text-slate-300">
                                <svg className="w-4 h-4 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span>30-min setup</span>
                            </div>
                            <div className="flex items-center justify-center gap-2 text-slate-300">
                                <svg className="w-4 h-4 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span>Free migration</span>
                            </div>
                            <div className="flex items-center justify-center gap-2 text-slate-300">
                                <svg className="w-4 h-4 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span>14-day free trial</span>
                            </div>
                        </div>

                        <div className="mt-8 flex flex-col sm:flex-row justify-center items-center gap-4">
                            <a href="#" className="w-full sm:w-auto bg-julep-green text-white font-bold px-8 py-4 rounded-lg hover:bg-opacity-90 transition-all shadow-lg text-lg inline-block relative overflow-hidden group">
                                <span className="relative z-10">Start Free 14-Day Trial</span>
                                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity"></div>
                            </a>
                            <a href="#" className="w-full sm:w-auto bg-white/10 text-white font-bold px-8 py-4 rounded-lg hover:bg-white/20 transition-all shadow-lg text-lg border border-white/20 flex items-center gap-2">
                                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                </svg>
                                Book Live Demo
                            </a>
                        </div>

                        <div className="mt-6 text-sm text-slate-400">
                            <div className="flex items-center justify-center gap-4">
                                <span>✓ No credit card required</span>
                                <span>✓ Setup in 30 minutes</span>
                                <span>✓ Cancel anytime</span>
                            </div>
                        </div>

                        {/* Social Proof */}
                        <div className="mt-8 flex items-center justify-center gap-2 text-slate-400 text-sm">
                            <div className="flex -space-x-2">
                                <img className="w-8 h-8 rounded-full border-2 border-julep-dark" src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=100&h=100&auto=format&fit=crop" alt="Customer" />
                                <img className="w-8 h-8 rounded-full border-2 border-julep-dark" src="https://images.unsplash.com/photo-1568602471122-7832951cc4c5?q=80&w=100&h=100&auto=format&fit=crop" alt="Customer" />
                                <img className="w-8 h-8 rounded-full border-2 border-julep-dark" src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&h=100&auto=format&fit=crop" alt="Customer" />
                            </div>
                            <span>Join 500+ nonprofits already using Julep</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

export default CTA;
