import React from 'react';

const BenefitCard: React.FC<{ icon: React.ReactNode; title: string; children: React.ReactNode }> = ({ icon, title, children }) => (
    <div className="bg-white p-8 rounded-xl shadow-lg border border-slate-100 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
        <div className="flex items-center justify-center h-16 w-16 rounded-full bg-julep-green/10 text-julep-green">
            {icon}
        </div>
        <h3 className="mt-6 text-2xl font-bold text-julep-dark">{title}</h3>
        <p className="mt-2 text-julep-gray">{children}</p>
    </div>
);

const Benefits: React.FC = () => {
    return (
        <section className="py-20 bg-slate-50">
            <div className="container mx-auto px-6">
                <div className="text-center max-w-3xl mx-auto">
                    <h2 className="text-3xl md:text-4xl font-bold text-julep-dark">
                        Focus on Your Mission, Not Your Software
                    </h2>
                    <p className="mt-4 text-lg text-julep-gray">
                        Julep is designed to help your team be more effective and efficient, driving real results for your cause.
                    </p>
                </div>
                <div className="mt-12 grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <BenefitCard
                        icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /></svg>}
                        title="Raise More Money"
                    >
                        With streamlined donation forms, powerful email tools, and a complete view of your donors, you'll have everything you need to grow your fundraising.
                    </BenefitCard>
                    <BenefitCard
                        icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>}
                        title="Save Time & Frustration"
                    >
                        Automate manual tasks, eliminate duplicate data entry, and manage all your fundraising activities from one place. Free up your team to build relationships.
                    </BenefitCard>
                    <BenefitCard
                        icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.085a2 2 0 00-1.707.953L5 10m9 0a2 2 0 012 2v5a2 2 0 01-2 2h-2.5" /></svg>}
                        title="A Joy to Use"
                    >
                        Julep is beautiful, intuitive, and fast. It's software you and your team will actually love using every day, leading to higher adoption and better data.
                    </BenefitCard>
                </div>
            </div>
        </section>
    );
};

export default Benefits;