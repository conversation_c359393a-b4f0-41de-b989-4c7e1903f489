
import React from 'react';

const Logo = () => (
    <svg className="w-8 h-8 text-julep-green" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path d="M17.3,3.8C15,2.7,12.4,3.4,10.7,5.2c-2.4,2.5-2.2,6.5,0.4,8.8l-5.7,5.7c-0.4,0.4-0.4,1,0,1.4s1,0.4,1.4,0l5.7-5.7 c2.3,2.6,6.3,2.8,8.8,0.4c1.8-1.7,2.5-4.3,1.4-6.6C21.7,7.1,19.5,4.9,17.3,3.8z M12.1,12.5c-1.5-1.5-1.5-3.9,0-5.4 c1.5-1.5,3.9-1.5,5.4,0C18.3,7.9,18.4,9,18.1,10c-0.1,0.4-0.4,0.7-0.7,0.8c-0.4,0.1-0.8,0-1.1-0.3l-2.8-2.8 c-0.4-0.4-1-0.4-1.4,0s-0.4,1,0,1.4l2.8,2.8c0.3,0.3,0.4,0.7,0.3,1.1c-0.1,0.4-0.4,0.7-0.8,0.7c-1-0.3-2.1-0.2-2.9,0.7 C12.5,13.9,12.5,13.2,12.1,12.5z"/>
    </svg>
);

const SocialIcon: React.FC<{ href: string; children: React.ReactNode, 'aria-label': string }> = ({ href, children, 'aria-label': ariaLabel }) => (
    <a href={href} aria-label={ariaLabel} className="text-slate-400 hover:text-julep-green transition-colors">
        {children}
    </a>
);

const FooterLink: React.FC<{ href: string; children: React.ReactNode }> = ({ href, children }) => (
    <li>
        <a href={href} className="text-slate-400 hover:text-julep-green transition-colors">{children}</a>
    </li>
);

const Footer: React.FC = () => {
    return (
        <footer className="bg-julep-dark text-white pt-48 pb-12">
            <div className="container mx-auto px-6">
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8">
                    <div className="col-span-2 md:col-span-4 lg:col-span-1 mb-8 lg:mb-0">
                        <div className="flex items-center">
                            <Logo />
                            <span className="text-2xl font-bold text-white ml-2">Julep</span>
                        </div>
                        <p className="mt-4 text-slate-400">The all-in-one fundraising platform for nonprofits.</p>
                    </div>
                    <div>
                        <h3 className="font-semibold tracking-wider uppercase">Product</h3>
                        <ul className="mt-4 space-y-3">
                            <FooterLink href="#">Features</FooterLink>
                            <FooterLink href="#">Pricing</FooterLink>
                            <FooterLink href="#">Integrations</FooterLink>
                            <FooterLink href="#">Book a Demo</FooterLink>
                        </ul>
                    </div>
                    <div>
                        <h3 className="font-semibold tracking-wider uppercase">Company</h3>
                        <ul className="mt-4 space-y-3">
                            <FooterLink href="#">About Us</FooterLink>
                            <FooterLink href="#">Careers</FooterLink>
                            <FooterLink href="#">Contact</FooterLink>
                        </ul>
                    </div>
                    <div>
                        <h3 className="font-semibold tracking-wider uppercase">Resources</h3>
                        <ul className="mt-4 space-y-3">
                            <FooterLink href="#">Blog</FooterLink>
                            <FooterLink href="#">Guides</FooterLink>
                            <FooterLink href="#">Case Studies</FooterLink>
                            <FooterLink href="#">Help Center</FooterLink>
                        </ul>
                    </div>
                    <div>
                        <h3 className="font-semibold tracking-wider uppercase">Legal</h3>
                        <ul className="mt-4 space-y-3">
                            <FooterLink href="#">Privacy Policy</FooterLink>
                            <FooterLink href="#">Terms of Service</FooterLink>
                        </ul>
                    </div>
                </div>
                <div className="mt-12 border-t border-julep-gray/50 pt-8 flex flex-col sm:flex-row justify-between items-center">
                    <p className="text-slate-400 text-sm order-2 sm:order-1 mt-4 sm:mt-0">&copy; {new Date().getFullYear()} Julep CRM. All rights reserved.</p>
                    <div className="flex space-x-6 order-1 sm:order-2">
                         <SocialIcon href="#" aria-label="Julep on X">
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/></svg>
                        </SocialIcon>
                        <SocialIcon href="#" aria-label="Julep on LinkedIn">
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fillRule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clipRule="evenodd"/></svg>
                        </SocialIcon>
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
