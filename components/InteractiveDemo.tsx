import React, { useState } from 'react';

const InteractiveDemo: React.FC = () => {
    const [activeTab, setActiveTab] = useState('dashboard');

    const demoScreens = {
        dashboard: {
            title: 'Fundraising Dashboard',
            description: 'Get a complete overview of your fundraising performance with real-time metrics and insights.',
            image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=1200&auto=format&fit=crop',
            features: [
                'Real-time donation tracking',
                'Donor engagement metrics',
                'Campaign performance analytics',
                'Goal progress visualization'
            ]
        },
        donors: {
            title: 'Donor Management',
            description: 'Build stronger relationships with a complete 360-degree view of every donor interaction.',
            image: 'https://images.unsplash.com/photo-1579532537598-459ecdaf39cc?q=80&w=1200&auto=format&fit=crop',
            features: [
                'Complete donor profiles',
                'Interaction history tracking',
                'Automated follow-up reminders',
                'Segmentation and targeting'
            ]
        },
        campaigns: {
            title: 'Email Campaigns',
            description: 'Create beautiful, personalized emails that inspire action and drive donations.',
            image: 'https://images.unsplash.com/photo-1586953208448-b95a89798f02?q=80&w=1200&auto=format&fit=crop',
            features: [
                'Drag-and-drop email builder',
                'Automated drip campaigns',
                'A/B testing capabilities',
                'Performance analytics'
            ]
        },
        donations: {
            title: 'Donation Pages',
            description: 'Build unlimited, mobile-friendly donation pages that convert visitors into supporters.',
            image: 'https://images.unsplash.com/photo-1593113646773-ae18c6451d3e?q=80&w=1200&auto=format&fit=crop',
            features: [
                'Mobile-optimized forms',
                'Recurring donation options',
                'Custom branding',
                'Secure payment processing'
            ]
        }
    };

    const tabs = [
        { id: 'dashboard', label: 'Dashboard', icon: '📊' },
        { id: 'donors', label: 'Donors', icon: '👥' },
        { id: 'campaigns', label: 'Campaigns', icon: '📧' },
        { id: 'donations', label: 'Donations', icon: '💝' }
    ];

    return (
        <section className="py-20 bg-white">
            <div className="container mx-auto px-6">
                <div className="text-center max-w-3xl mx-auto mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-julep-dark">
                        See Julep in Action
                    </h2>
                    <p className="mt-4 text-lg text-julep-gray">
                        Explore our platform with this interactive demo. Click through the different sections to see how Julep can transform your fundraising.
                    </p>
                </div>

                {/* Interactive Demo */}
                <div className="max-w-6xl mx-auto">
                    {/* Tab Navigation */}
                    <div className="flex flex-wrap justify-center gap-4 mb-8">
                        {tabs.map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all ${
                                    activeTab === tab.id
                                        ? 'bg-julep-green text-white shadow-lg'
                                        : 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                                }`}
                            >
                                <span className="text-lg">{tab.icon}</span>
                                <span>{tab.label}</span>
                            </button>
                        ))}
                    </div>

                    {/* Demo Content */}
                    <div className="bg-slate-50 rounded-2xl p-8 md:p-12">
                        <div className="grid md:grid-cols-2 gap-12 items-center">
                            <div>
                                <h3 className="text-2xl md:text-3xl font-bold text-julep-dark mb-4">
                                    {demoScreens[activeTab as keyof typeof demoScreens].title}
                                </h3>
                                <p className="text-lg text-slate-600 mb-6">
                                    {demoScreens[activeTab as keyof typeof demoScreens].description}
                                </p>
                                
                                <ul className="space-y-3 mb-8">
                                    {demoScreens[activeTab as keyof typeof demoScreens].features.map((feature, index) => (
                                        <li key={index} className="flex items-center gap-3">
                                            <svg className="w-5 h-5 text-julep-green flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-slate-700">{feature}</span>
                                        </li>
                                    ))}
                                </ul>
                                
                                <div className="flex flex-col sm:flex-row gap-4">
                                    <a href="#" className="bg-julep-green text-white font-bold px-6 py-3 rounded-lg hover:bg-opacity-90 transition-all shadow-lg text-center">
                                        Try This Feature
                                    </a>
                                    <a href="#" className="bg-white text-julep-dark font-bold px-6 py-3 rounded-lg hover:bg-slate-100 transition-all shadow-lg border border-slate-200 text-center">
                                        Book Full Demo
                                    </a>
                                </div>
                            </div>
                            
                            <div className="relative">
                                <div className="relative bg-white rounded-xl shadow-2xl border-8 border-white overflow-hidden">
                                    <img 
                                        src={demoScreens[activeTab as keyof typeof demoScreens].image}
                                        alt={`${demoScreens[activeTab as keyof typeof demoScreens].title} interface`}
                                        className="w-full h-auto"
                                    />
                                    
                                    {/* Overlay with play button */}
                                    <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity cursor-pointer">
                                        <div className="bg-white rounded-full p-4 shadow-lg">
                                            <svg className="w-8 h-8 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                
                                {/* Floating metrics based on active tab */}
                                {activeTab === 'dashboard' && (
                                    <>
                                        <div className="absolute -top-4 -right-4 bg-white p-3 rounded-lg shadow-lg border border-slate-100">
                                            <div className="text-xl font-bold text-julep-green">$12,450</div>
                                            <div className="text-xs text-slate-600">This Month</div>
                                        </div>
                                        <div className="absolute -bottom-4 -left-4 bg-white p-3 rounded-lg shadow-lg border border-slate-100">
                                            <div className="text-xl font-bold text-julep-green">156</div>
                                            <div className="text-xs text-slate-600">New Donors</div>
                                        </div>
                                    </>
                                )}
                                
                                {activeTab === 'donors' && (
                                    <div className="absolute -top-4 -right-4 bg-white p-3 rounded-lg shadow-lg border border-slate-100">
                                        <div className="text-xl font-bold text-julep-green">2,847</div>
                                        <div className="text-xs text-slate-600">Active Donors</div>
                                    </div>
                                )}
                                
                                {activeTab === 'campaigns' && (
                                    <div className="absolute -bottom-4 -right-4 bg-white p-3 rounded-lg shadow-lg border border-slate-100">
                                        <div className="text-xl font-bold text-julep-green">24.5%</div>
                                        <div className="text-xs text-slate-600">Open Rate</div>
                                    </div>
                                )}
                                
                                {activeTab === 'donations' && (
                                    <div className="absolute -top-4 -left-4 bg-white p-3 rounded-lg shadow-lg border border-slate-100">
                                        <div className="text-xl font-bold text-julep-green">89%</div>
                                        <div className="text-xs text-slate-600">Conversion Rate</div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* CTA */}
                <div className="text-center mt-16">
                    <h3 className="text-2xl font-bold text-julep-dark mb-4">
                        Ready to see your own data here?
                    </h3>
                    <p className="text-lg text-slate-600 mb-8 max-w-2xl mx-auto">
                        Schedule a personalized demo and we'll show you exactly how Julep will work with your nonprofit's data and workflows.
                    </p>
                    <a href="#" className="bg-julep-green text-white font-bold px-8 py-4 rounded-lg hover:bg-opacity-90 transition-all shadow-lg text-lg inline-block">
                        Schedule Your Personal Demo
                    </a>
                </div>
            </div>
        </section>
    );
};

export default InteractiveDemo;
