
import React, { useState } from 'react';
import { FEATURES } from '../constants';

const FeatureTabs: React.FC = () => {
    const [activeTab, setActiveTab] = useState(0);
    const activeFeature = FEATURES[activeTab];

    return (
        <section className="py-20 bg-slate-50">
            <div className="container mx-auto px-6">
                <div className="text-center max-w-3xl mx-auto">
                    <h2 className="text-3xl md:text-4xl font-bold text-julep-dark">
                        Everything you need, all in one place
                    </h2>
                    <p className="mt-4 text-lg text-julep-gray">
                        Julep replaces a handful of expensive, disconnected tools with one simple, affordable platform.
                    </p>
                </div>

                <div className="mt-12 flex flex-col lg:flex-row gap-12 items-center">
                    {/* Tabs */}
                    <div className="w-full lg:w-1/3">
                        <div className="flex flex-col space-y-4">
                            {FEATURES.map((feature, index) => (
                                <button
                                    key={feature.name}
                                    onClick={() => setActiveTab(index)}
                                    className={`p-6 rounded-lg text-left transition-all duration-300 ${activeTab === index ? 'bg-white shadow-lg scale-105' : 'bg-transparent hover:bg-white/60'}`}
                                >
                                    <div className="flex items-center">
                                        <div className={`p-3 rounded-md ${activeTab === index ? 'bg-julep-green text-white' : 'bg-slate-200 text-julep-green'}`}>
                                            {feature.icon}
                                        </div>
                                        <h3 className="ml-4 text-lg font-semibold text-julep-dark">{feature.name}</h3>
                                    </div>
                                    {activeTab === index && (
                                        <p className="mt-3 text-julep-gray text-base leading-relaxed">
                                            {feature.description}
                                        </p>
                                    )}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Content */}
                    <div className="w-full lg:w-2/3">
                         <div className="relative aspect-video rounded-xl overflow-hidden shadow-2xl border-8 border-slate-200">
                             <img 
                                src={activeFeature.image} 
                                alt={activeFeature.name}
                                className="w-full h-full object-cover transition-opacity duration-500"
                                key={activeFeature.image} // Force re-render on image change for transition
                             />
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default FeatureTabs;
