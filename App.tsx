
import React from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import Logos from './components/Logos';
import FeatureTabs from './components/FeatureTabs';
import EmailGenerator from './components/EmailGenerator';
import Benefits from './components/Benefits';
import Testimonials from './components/Testimonials';
import CTA from './components/CTA';
import Footer from './components/Footer';

const App: React.FC = () => {
  return (
    <div className="bg-white text-julep-gray font-sans antialiased">
      <Header />
      <main>
        <Hero />
        <Logos />
        <FeatureTabs />
        <EmailGenerator />
        <Benefits />
        <Testimonials />
        <CTA />
      </main>
      <Footer />
    </div>
  );
};

export default App;
