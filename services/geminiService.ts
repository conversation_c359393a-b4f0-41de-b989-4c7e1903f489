
import { GoogleGenAI } from "@google/genai";

if (!process.env.API_KEY) {
  throw new Error("API_KEY environment variable not set");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

export async function generateEmailCopy(campaignGoal: string): Promise<string> {
  if (!campaignGoal.trim()) {
    return "Please enter a campaign goal to generate an email.";
  }

  const prompt = `
    You are an expert fundraising copywriter for nonprofits. Your tone is inspiring, urgent, and empathetic.
    A user is running a fundraising campaign for the following cause: "${campaignGoal}".

    Generate a short, compelling fundraising email draft (around 150 words). The email should include:
    1. A powerful, attention-grabbing subject line.
    2. A clear opening that states the problem or need.
    3. A brief story or emotional connection to the cause.
    4. A clear call to action asking for a specific donation (e.g., "$25").
    5. A closing that expresses gratitude.

    Format the output as plain text. Start with "Subject: [Your Subject Line]" and then the body of the email on a new line.
  `;

  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: prompt,
      config: {
        temperature: 0.7,
        topP: 1.0,
      }
    });
    return response.text;
  } catch (error) {
    console.error("Error generating email copy:", error);
    return "Sorry, we couldn't generate the email copy at this time. Please try again later.";
  }
}
