
import React from 'react';

const Logo: React.FC<{ name: string; children: React.ReactNode }> = ({ name, children }) => (
    <div className="flex items-center justify-center h-12 text-slate-500 font-semibold text-lg grayscale opacity-60 hover:grayscale-0 hover:opacity-100 transition-all" title={name}>
        {children}
    </div>
);

const Logos: React.FC = () => {
    return (
        <div className="bg-white py-12 sm:py-16 border-b border-slate-100">
            <div className="container mx-auto px-6">
                <h2 className="text-center text-base font-semibold text-julep-gray uppercase tracking-wider">
                    Trusted by 500+ nonprofits worldwide
                </h2>
                <div className="mt-10 grid grid-cols-2 gap-y-10 gap-x-8 md:grid-cols-3 lg:grid-cols-6">
                    <Logo name="Veridian">
                        <svg viewBox="0 0 110 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-6 w-auto"><path d="M0 24V0H8.05063V9.89873H17.8481V0H25.8987V24H17.8481V13.8481H8.05063V24H0Z" fill="currentColor"/><path d="M54.546 24.0001V13.8482L44.2042 0H53.5839L59.0802 9.03801L64.5765 0H73.9562L63.6143 13.8482V24.0001H54.546Z" fill="currentColor"/><path d="M82.2608 24V0H100.311C105.922 0 110 4.12658 110 9.6962C110 15.2658 105.922 19.3924 100.311 19.3924H90.3134V24H82.2608ZM90.3134 13.8481H98.0127C100.716 13.8481 102.747 11.9747 102.747 9.6962C102.747 7.41772 100.716 5.5443 98.0127 5.5443H90.3134V13.8481Z" fill="currentColor"/><path d="M30.1266 24V0H47.1646V5.5443H38.1772V9.89873H46.3038V14.5443H38.1772V18.4557H47.5443V24H30.1266Z" fill="currentColor"/></svg>
                    </Logo>
                    <Logo name="Oasis">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-8 w-auto"><circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/><path d="M12 2C14.7404 2 17.1673 3.32125 18.7323 5.26773C17.1673 7.2142 14.7404 8.53545 12 8.53545C9.25962 8.53545 6.83269 7.2142 5.26773 5.26773C6.83269 3.32125 9.25962 2 12 2Z" fill="currentColor"/></svg>
                    </Logo>
                    <Logo name="Unity">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-8 w-auto"><path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinejoin="round"/><path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                    </Logo>
                    <Logo name="Kindred">
                         <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-8 w-auto"><path d="M17 10C17 13.866 12 19 12 19C12 19 7 13.866 7 10C7 6.13401 9.13401 4 12 4C14.866 4 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2"/><circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2"/></svg>
                    </Logo>
                    <Logo name="Summit">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-8 w-auto"><path d="M3 17L9 11L13 15L21 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M15 7H21V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                    </Logo>
                     <Logo name="Evergreen">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-8 w-auto"><path d="M12 2L2 22H22L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinejoin="round"/></svg>
                    </Logo>
                </div>

                {/* Trust Metrics */}
                <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                    <div className="text-center">
                        <div className="text-3xl font-bold text-julep-green">$50M+</div>
                        <div className="text-sm text-slate-600 mt-1">Donations Processed</div>
                    </div>
                    <div className="text-center">
                        <div className="text-3xl font-bold text-julep-green">500+</div>
                        <div className="text-sm text-slate-600 mt-1">Nonprofits Served</div>
                    </div>
                    <div className="text-center">
                        <div className="text-3xl font-bold text-julep-green">99.9%</div>
                        <div className="text-sm text-slate-600 mt-1">Uptime Guarantee</div>
                    </div>
                </div>

                {/* Security Badges */}
                <div className="mt-12 flex justify-center items-center gap-8 opacity-70">
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                        <svg className="w-5 h-5 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span>SOC 2 Compliant</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                        <svg className="w-5 h-5 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                        <span>256-bit SSL</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                        <svg className="w-5 h-5 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span>PCI Compliant</span>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Logos;
