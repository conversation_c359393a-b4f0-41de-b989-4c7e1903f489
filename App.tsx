
import React from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import Logos from './components/Logos';
import FeatureTabs from './components/FeatureTabs';
import EmailGenerator from './components/EmailGenerator';
import InteractiveDemo from './components/InteractiveDemo';
import Benefits from './components/Benefits';
import Testimonials from './components/Testimonials';
import Pricing from './components/Pricing';
import CTA from './components/CTA';
import Footer from './components/Footer';
import ConversionOptimization from './components/ConversionOptimization';

const App: React.FC = () => {
  return (
    <div className="bg-white text-julep-gray font-sans antialiased">
      <Header />
      <main>
        <Hero />
        <Logos />
        <FeatureTabs />
        <EmailGenerator />
        <InteractiveDemo />
        <Benefits />
        <Testimonials />
        <Pricing />
        <CTA />
      </main>
      <Footer />
      <ConversionOptimization />
    </div>
  );
};

export default App;
