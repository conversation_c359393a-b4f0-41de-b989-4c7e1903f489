import React from 'react';

const BenefitCard: React.FC<{ icon: React.ReactNode; title: string; children: React.ReactNode }> = ({ icon, title, children }) => (
    <div className="bg-white p-8 rounded-xl shadow-lg border border-slate-100 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
        <div className="flex items-center justify-center h-16 w-16 rounded-full bg-julep-green/10 text-julep-green">
            {icon}
        </div>
        <h3 className="mt-6 text-2xl font-bold text-julep-dark">{title}</h3>
        <p className="mt-2 text-julep-gray">{children}</p>
    </div>
);

const Benefits: React.FC = () => {
    return (
        <section className="py-20 bg-slate-50">
            <div className="container mx-auto px-6">
                <div className="text-center max-w-3xl mx-auto">
                    <h2 className="text-3xl md:text-4xl font-bold text-julep-dark">
                        Real Results for Real Nonprofits
                    </h2>
                    <p className="mt-4 text-lg text-julep-gray">
                        See the measurable impact Julep has on organizations just like yours.
                    </p>
                </div>
                <div className="mt-12 grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <BenefitCard
                        icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /></svg>}
                        title="30% More Donations"
                    >
                        Our customers see an average 30% increase in donations within 6 months. Better donor insights, automated follow-ups, and optimized donation forms drive real results.
                    </BenefitCard>
                    <BenefitCard
                        icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>}
                        title="5 Hours Saved Weekly"
                    >
                        Eliminate manual data entry, automate donor communications, and streamline reporting. Your team saves 5+ hours per week to focus on mission-critical work.
                    </BenefitCard>
                    <BenefitCard
                        icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>}
                        title="95% Team Adoption"
                    >
                        Unlike complex CRMs that sit unused, 95% of teams actively use Julep daily. Intuitive design means better data quality and stronger donor relationships.
                    </BenefitCard>
                </div>

                {/* Success Stories */}
                <div className="mt-20">
                    <h3 className="text-2xl font-bold text-julep-dark text-center mb-12">
                        Success Stories
                    </h3>
                    <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                        <div className="bg-white p-8 rounded-xl shadow-lg">
                            <div className="flex items-center gap-4 mb-4">
                                <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=100&h=100&auto=format&fit=crop" alt="Sarah Johnson" className="w-12 h-12 rounded-full" />
                                <div>
                                    <div className="font-semibold text-julep-dark">Hope Foundation</div>
                                    <div className="text-sm text-slate-600">Increased donations by 45%</div>
                                </div>
                            </div>
                            <p className="text-slate-700 italic">
                                "We consolidated four different tools into Julep and saw immediate results. Our fundraising increased 45% in the first year, and our team is so much more efficient."
                            </p>
                        </div>

                        <div className="bg-white p-8 rounded-xl shadow-lg">
                            <div className="flex items-center gap-4 mb-4">
                                <img src="https://images.unsplash.com/photo-1568602471122-7832951cc4c5?q=80&w=100&h=100&auto=format&fit=crop" alt="Michael Chen" className="w-12 h-12 rounded-full" />
                                <div>
                                    <div className="font-semibold text-julep-dark">Big Brothers Big Sisters</div>
                                    <div className="text-sm text-slate-600">Saved 8 hours per week</div>
                                </div>
                            </div>
                            <p className="text-slate-700 italic">
                                "The automation features alone save us 8 hours every week. We can now focus on building relationships with donors instead of managing spreadsheets."
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Benefits;