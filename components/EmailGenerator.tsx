import React, { useState, useCallback } from 'react';
import { generateEmailCopy } from '../services/geminiService';

const CopyIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
    </svg>
);

const CheckIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-julep-green" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
    </svg>
);


const EmailGenerator: React.FC = () => {
    const [campaignGoal, setCampaignGoal] = useState('');
    const [generatedEmail, setGeneratedEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [isCopied, setIsCopied] = useState(false);

    const handleGenerate = useCallback(async () => {
        if (!campaignGoal.trim()) {
            setError('Please enter a campaign goal.');
            return;
        }
        setIsLoading(true);
        setError('');
        setGeneratedEmail('');
        setIsCopied(false);
        try {
            const result = await generateEmailCopy(campaignGoal);
            setGeneratedEmail(result);
        } catch (e: any) {
            setError('An error occurred. Please try again.');
            console.error(e);
        } finally {
            setIsLoading(false);
        }
    }, [campaignGoal]);
    
    const formattedEmail = React.useMemo(() => {
        if (!generatedEmail) return null;
        const parts = generatedEmail.split('\n');
        const subjectLine = parts.find(p => p.toLowerCase().startsWith('subject:')) || '';
        const body = parts.filter(p => !p.toLowerCase().startsWith('subject:')).join('\n').trim();
        return { subjectLine, body };
    }, [generatedEmail]);

    const handleCopy = () => {
        if (!generatedEmail) return;
        navigator.clipboard.writeText(generatedEmail);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
    };

    return (
        <section className="py-20 bg-white">
            <div className="container mx-auto px-6">
                <div className="bg-julep-dark rounded-2xl p-8 md:p-12 lg:p-16 text-white shadow-2xl flex flex-col lg:flex-row gap-12">
                    <div className="lg:w-1/2">
                        <h2 className="text-3xl md:text-4xl font-bold">Struggling with Donor Emails?</h2>
                        <p className="mt-4 text-lg text-slate-300">
                            Get a head start with AI. Tell us your fundraising goal, and we'll instantly draft a compelling email to inspire your supporters.
                        </p>
                        <div className="mt-8">
                            <label htmlFor="campaign-goal" className="font-semibold text-slate-200">
                                What are you fundraising for?
                            </label>
                            <input
                                id="campaign-goal"
                                type="text"
                                value={campaignGoal}
                                onChange={(e) => setCampaignGoal(e.target.value)}
                                placeholder="e.g., a new community playground"
                                className="mt-2 w-full p-4 rounded-lg bg-julep-gray/50 text-white placeholder-slate-400 focus:ring-2 focus:ring-julep-green focus:outline-none transition-all"
                            />
                            <button
                                onClick={handleGenerate}
                                disabled={isLoading}
                                className="mt-4 w-full bg-julep-green text-white font-bold py-4 rounded-lg hover:bg-opacity-90 transition-all disabled:bg-opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                            >
                                {isLoading ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Generating...
                                    </>
                                ) : 'Generate Email Draft'}
                            </button>
                            {error && <p className="mt-2 text-red-400">{error}</p>}
                        </div>
                    </div>
                    <div className="lg:w-1/2">
                        <div className="bg-white rounded-lg h-full p-6 text-slate-800 shadow-inner flex flex-col relative">
                           <div className="flex justify-between items-start">
                                <h3 className="text-xl font-bold text-julep-dark mb-4">Your AI-Generated Draft</h3>
                                {formattedEmail && (
                                    <button onClick={handleCopy} className={`flex items-center gap-2 text-sm font-semibold py-1 px-2 rounded ${isCopied ? 'text-julep-green' : 'text-julep-gray hover:bg-slate-100'}`}>
                                        {isCopied ? <><CheckIcon /> Copied!</> : <><CopyIcon /> Copy</>}
                                    </button>
                                )}
                           </div>
                           
                           {isLoading && (
                            <div className="flex-grow flex items-center justify-center">
                                <div className="space-y-4 w-full">
                                    <div className="h-4 bg-slate-200 rounded animate-pulse"></div>
                                    <div className="h-4 bg-slate-200 rounded animate-pulse w-5/6"></div>
                                    <div className="h-20 bg-slate-200 rounded animate-pulse mt-6"></div>
                                    <div className="h-4 bg-slate-200 rounded animate-pulse w-1/2"></div>
                                </div>
                            </div>
                           )}
                           {!isLoading && !formattedEmail && (
                                <div className="flex-grow flex items-center justify-center text-center text-slate-400">
                                    <p>Your generated email will appear here.</p>
                                </div>
                           )}
                           {formattedEmail && (
                                <div className="flex-grow font-sans text-base text-slate-600 space-y-4 whitespace-pre-wrap overflow-y-auto">
                                    <div className="bg-slate-50 p-3 rounded-md">
                                        <p className="font-semibold text-julep-dark">{formattedEmail.subjectLine}</p>
                                    </div>
                                    <p className="p-1">{formattedEmail.body}</p>
                                </div>
                           )}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default EmailGenerator;