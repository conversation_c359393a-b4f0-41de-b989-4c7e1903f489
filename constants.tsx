
import React from 'react';
import { Feature, Testimonial } from './types';

const UsersIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M15 21a6 6 0 00-9-5.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-3-5.197" />
    </svg>
);

const MailIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
    </svg>
);

const GlobeIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2h10a2 2 0 002-2v-1a2 2 0 012-2h1.945M7.704 4.343a9 9 0 0110.592 0m-10.592 0a9 9 0 00-3.343 4.998M7.704 4.343a9 9 0 013.343-4.998m7.249 4.998a9 9 0 013.343 4.998m-3.343-4.998a9 9 0 00-10.592 0" />
    </svg>
);

const ChartBarIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
);

export const FEATURES: Feature[] = [
    {
        name: 'Donor Management',
        description: 'Get a complete 360-degree view of your donors. Track every interaction, donation, and communication in one central place to build stronger relationships.',
        icon: <UsersIcon />,
        image: 'https://images.unsplash.com/photo-1579532537598-459ecdaf39cc?q=80&w=1200&auto=format&fit=crop'
    },
    {
        name: 'Email Marketing',
        description: 'Create beautiful, personalized emails that inspire action. Use our powerful segmentation tools to send the right message to the right people at the right time.',
        icon: <MailIcon />,
        image: 'https://images.unsplash.com/photo-1586953208448-b95a89798f02?q=80&w=1200&auto=format&fit=crop'
    },
    {
        name: 'Online Donations',
        description: 'Build unlimited, mobile-friendly donation pages in minutes. Accept one-time and recurring gifts securely, making it easy for supporters to give.',
        icon: <GlobeIcon />,
        image: 'https://images.unsplash.com/photo-1593113646773-ae18c6451d3e?q=80&w=1200&auto=format&fit=crop'
    },
    {
        name: 'Reporting & Analytics',
        description: 'Make data-driven decisions with customizable reports and dashboards. Understand your fundraising performance and uncover insights for growth.',
        icon: <ChartBarIcon />,
        image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=1200&auto=format&fit=crop'
    }
];

export const TESTIMONIALS: Testimonial[] = [
    {
        quote: "Julep has been a game-changer for us. We've consolidated four different tools into one, saving us countless hours and headaches. Our fundraising has increased by over 30% since we switched.",
        name: 'Sarah Johnson',
        title: 'Executive Director',
        company: 'Hope Foundation',
        avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=100&h=100&auto=format&fit=crop'
    },
    {
        quote: "The user interface is so intuitive. Our team was up and running in a day, not weeks. It's the first CRM our fundraisers actually enjoy using, which means better data and better results.",
        name: 'Michael Chen',
        title: 'Development Manager',
        company: 'Big Brothers Big Sisters',
        avatar: 'https://images.unsplash.com/photo-1568602471122-7832951cc4c5?q=80&w=100&h=100&auto=format&fit=crop'
    },
    {
        quote: "Switching to Julep was the best decision we made last year. The support team is incredible, and the platform has everything we need to grow our mission. I can't recommend it enough.",
        name: 'Emily Rodriguez',
        title: 'Founder',
        company: 'Paws for a Cause',
        avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&h=100&auto=format&fit=crop'
    }
];
