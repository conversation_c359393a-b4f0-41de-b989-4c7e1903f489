
import React, { useState } from 'react';

const Logo = () => (
    <svg className="w-8 h-8 text-julep-green" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path d="M17.3,3.8C15,2.7,12.4,3.4,10.7,5.2c-2.4,2.5-2.2,6.5,0.4,8.8l-5.7,5.7c-0.4,0.4-0.4,1,0,1.4s1,0.4,1.4,0l5.7-5.7 c2.3,2.6,6.3,2.8,8.8,0.4c1.8-1.7,2.5-4.3,1.4-6.6C21.7,7.1,19.5,4.9,17.3,3.8z M12.1,12.5c-1.5-1.5-1.5-3.9,0-5.4 c1.5-1.5,3.9-1.5,5.4,0C18.3,7.9,18.4,9,18.1,10c-0.1,0.4-0.4,0.7-0.7,0.8c-0.4,0.1-0.8,0-1.1-0.3l-2.8-2.8 c-0.4-0.4-1-0.4-1.4,0s-0.4,1,0,1.4l2.8,2.8c0.3,0.3,0.4,0.7,0.3,1.1c-0.1,0.4-0.4,0.7-0.8,0.7c-1-0.3-2.1-0.2-2.9,0.7 C12.5,13.9,12.5,13.2,12.1,12.5z"/>
    </svg>
);


const Header: React.FC = () => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <header className="bg-white/80 backdrop-blur-md sticky top-0 z-50 border-b border-slate-200">
            <div className="container mx-auto px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                         <Logo />
                        <span className="text-2xl font-bold text-julep-dark ml-2">Julep</span>
                    </div>
                    <nav className="hidden md:flex items-center space-x-8">
                        <a href="#" className="text-julep-gray hover:text-julep-green transition-colors">Pricing</a>
                        <a href="#" className="text-julep-gray hover:text-julep-green transition-colors">Blog</a>
                        <a href="#" className="text-julep-gray hover:text-julep-green transition-colors">Sign In</a>
                        <a href="#" className="bg-julep-green text-white font-semibold px-5 py-2 rounded-lg hover:bg-opacity-90 transition-all shadow-sm">
                            Get a Demo
                        </a>
                    </nav>
                    <div className="md:hidden">
                        <button onClick={() => setIsOpen(!isOpen)} aria-label="Open menu" aria-expanded={isOpen}>
                            <svg className="w-6 h-6 text-julep-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                        </button>
                    </div>
                </div>
                {isOpen && (
                    <div className="md:hidden mt-4">
                        <nav className="flex flex-col space-y-4">
                            <a href="#" className="text-julep-gray hover:text-julep-green transition-colors">Pricing</a>
                            <a href="#" className="text-julep-gray hover:text-julep-green transition-colors">Blog</a>
                            <a href="#" className="text-julep-gray hover:text-julep-green transition-colors">Sign In</a>
                            <a href="#" className="bg-julep-green text-white font-semibold px-5 py-2 rounded-lg hover:bg-opacity-90 transition-all text-center">
                                Get a Demo
                            </a>
                        </nav>
                    </div>
                )}
            </div>
        </header>
    );
}

export default Header;
