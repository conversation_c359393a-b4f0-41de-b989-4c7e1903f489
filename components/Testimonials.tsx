import React from 'react';
import { TESTIMONIALS } from '../constants';
import { Testimonial } from '../types';


const TestimonialCard: React.FC<{ testimonial: Testimonial }> = ({ testimonial }) => (
  <div className="bg-white p-8 rounded-xl shadow-lg flex flex-col h-full hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
    <div className="flex-grow">
      <svg className="w-10 h-10 text-julep-green/20" viewBox="0 0 44 34" fill="currentColor">
        <path d="M14.212 34S0 28.232 0 14.212C0 5.484 5.484 0 14.212 0s12.072 5.094 12.072 14.212c0 8.34-4.704 10.536-6.276 10.536S14.212 34 14.212 34zm23.52 0S18.42 28.232 18.42 14.212C18.42 5.484 23.904 0 32.628 0s12.072 5.094 12.072 14.212c0 8.34-4.704 10.536-6.276 10.536S37.732 34 37.732 34z" />
      </svg>
      <p className="mt-6 text-lg text-julep-gray leading-relaxed">
        "{testimonial.quote}"
      </p>
    </div>
    <div className="mt-6 pt-6 border-t border-slate-200">
      <div className="flex items-center">
        <img className="h-14 w-14 rounded-full object-cover" src={testimonial.avatar} alt={testimonial.name} />
        <div className="ml-4">
          <p className="font-bold text-julep-dark">{testimonial.name}</p>
          <p className="text-sm text-julep-gray">{testimonial.title}, {testimonial.company}</p>
        </div>
      </div>
    </div>
  </div>
);


const Testimonials: React.FC = () => {
    return (
        <section className="py-20 bg-white">
            <div className="container mx-auto px-6">
                <div className="text-center max-w-3xl mx-auto">
                    <h2 className="text-3xl md:text-4xl font-bold text-julep-dark">
                        Why Fundraisers Love Julep
                    </h2>
                </div>
                <div className="mt-12 grid lg:grid-cols-3 gap-8">
                    {TESTIMONIALS.map((testimonial, index) => (
                        <TestimonialCard key={index} testimonial={testimonial} />
                    ))}
                </div>
            </div>
        </section>
    );
};

export default Testimonials;