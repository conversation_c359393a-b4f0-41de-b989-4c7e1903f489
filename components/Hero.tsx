import React from 'react';

const Hero: React.FC = () => {
    return (
        <section className="bg-slate-50 overflow-hidden">
            <div className="container mx-auto px-6 pt-24 pb-16 md:py-32">
                {/* Trust Bar */}
                <div style={{ animationDelay: '0.1s', animationFillMode: 'backwards' }} className="text-center mb-8 animate-fadeInUp opacity-0">
                    <div className="inline-flex items-center gap-2 bg-julep-green/10 text-julep-green px-4 py-2 rounded-full text-sm font-medium">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Trusted by 500+ nonprofits • Raised $50M+ in donations
                    </div>
                </div>

                <div className="grid md:grid-cols-2 gap-12 items-center">
                    <div className="text-center md:text-left">
                        <h1 style={{ animationDelay: '0.2s', animationFillMode: 'backwards' }} className="text-4xl md:text-5xl lg:text-6xl font-extrabold text-julep-dark leading-tight animate-fadeInUp opacity-0">
                            Raise 30% More Money <span className="text-julep-green">Without the Headaches</span>
                        </h1>
                        <p style={{ animationDelay: '0.4s', animationFillMode: 'backwards' }} className="mt-6 text-lg md:text-xl max-w-xl mx-auto md:mx-0 text-julep-gray animate-fadeInUp opacity-0">
                            The only CRM built specifically for nonprofit fundraising. Stop juggling spreadsheets and multiple tools. Julep helps you build stronger donor relationships and raise more money in less time.
                        </p>

                        {/* Key Benefits */}
                        <div style={{ animationDelay: '0.5s', animationFillMode: 'backwards' }} className="mt-6 space-y-2 animate-fadeInUp opacity-0">
                            <div className="flex items-center justify-center md:justify-start gap-2 text-julep-gray">
                                <svg className="w-5 h-5 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span>Set up in under 30 minutes</span>
                            </div>
                            <div className="flex items-center justify-center md:justify-start gap-2 text-julep-gray">
                                <svg className="w-5 h-5 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span>Replace 4+ tools with one platform</span>
                            </div>
                            <div className="flex items-center justify-center md:justify-start gap-2 text-julep-gray">
                                <svg className="w-5 h-5 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span>Free migration & onboarding support</span>
                            </div>
                        </div>

                        <div style={{ animationDelay: '0.6s', animationFillMode: 'backwards' }} className="mt-10 flex flex-col sm:flex-row justify-center md:justify-start items-center gap-4 animate-fadeInUp opacity-0">
                            <a href="#" className="w-full sm:w-auto bg-julep-green text-white font-bold px-8 py-4 rounded-lg hover:bg-opacity-90 transition-all shadow-lg text-lg relative overflow-hidden group">
                                <span className="relative z-10">Start Free 14-Day Trial</span>
                                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity"></div>
                            </a>
                            <a href="#" className="w-full sm:w-auto bg-white text-julep-dark font-bold px-8 py-4 rounded-lg hover:bg-slate-100 transition-all shadow-lg text-lg border border-slate-200 flex items-center gap-2">
                                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                </svg>
                                Watch 3-Min Demo
                            </a>
                        </div>
                        <div style={{ animationDelay: '0.8s', animationFillMode: 'backwards' }} className="mt-6 text-sm text-slate-500 animate-fadeInUp opacity-0">
                            <div className="flex items-center justify-center md:justify-start gap-4">
                                <span>✓ No credit card required</span>
                                <span>✓ Cancel anytime</span>
                                <span>✓ Free setup</span>
                            </div>
                        </div>
                    </div>
                    <div className="relative animate-float" style={{ animationDelay: '0.5s' }}>
                         <img
                            src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=1200&auto=format&fit=crop"
                            alt="Julep CRM dashboard showing donor analytics and fundraising metrics"
                            className="rounded-2xl shadow-2xl border-8 border-white"
                         />
                         {/* Floating metrics */}
                         <div className="absolute -top-4 -right-4 bg-white p-4 rounded-lg shadow-lg border border-slate-100">
                            <div className="text-2xl font-bold text-julep-green">+30%</div>
                            <div className="text-sm text-slate-600">Avg. Donation Increase</div>
                         </div>
                         <div className="absolute -bottom-4 -left-4 bg-white p-4 rounded-lg shadow-lg border border-slate-100">
                            <div className="text-2xl font-bold text-julep-green">5hrs</div>
                            <div className="text-sm text-slate-600">Saved Per Week</div>
                         </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

export default Hero;