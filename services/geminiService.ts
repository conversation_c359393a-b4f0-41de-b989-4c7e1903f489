
// Mock service for demonstration - replace with real API integration
export async function generateEmailCopy(campaignGoal: string): Promise<string> {
  if (!campaignGoal.trim()) {
    return "Please enter a campaign goal to generate an email.";
  }

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Generate contextual email based on campaign goal
  const templates = {
    playground: {
      subject: "Help Us Build a Safe Place for Kids to Play",
      body: `Dear Friend,

Right now, children in our community have nowhere safe to play. The old playground equipment was removed due to safety concerns, leaving kids with only concrete and busy streets.

Last week, 8-year-old <PERSON> asked her mom, "Why can't we have swings like the other neighborhoods?" It broke our hearts.

We're raising funds to build a new community playground where every child can laugh, play, and just be a kid. Your donation of $50 can sponsor playground equipment that will bring joy to hundreds of children for years to come.

Will you help us give our kids the childhood they deserve?

With gratitude,
The Community Center Team`
    },
    education: {
      subject: "Every Child Deserves Access to Quality Education",
      body: `Dear Supporter,

In our community, too many bright minds are being left behind simply because they lack access to educational resources.

Meet <PERSON>, a 10-year-old who walks 3 miles to school every day because her family can't afford transportation. Despite the challenges, she dreams of becoming a teacher to help other kids like her.

Your donation of $75 can provide school supplies, transportation assistance, and tutoring support for students like <PERSON>. Together, we can ensure that every child has the opportunity to learn and thrive.

Will you invest in a child's future today?

Thank you for believing in our kids,
Education First Foundation`
    },
    food: {
      subject: "No Child Should Go to Bed Hungry",
      body: `Dear Compassionate Friend,

Tonight, 1 in 4 children in our community will go to bed hungry. For many kids, the meals they receive at school are the only nutritious food they'll get all day.

When schools close for the weekend, families like the Johnsons struggle to put food on the table. Their 7-year-old son Tommy often asks, "Mom, when will we eat again?"

Your gift of $25 can provide weekend food backpacks for a child in need, ensuring they have nutritious meals when school is out. No child should have to worry about their next meal.

Can you help us feed a hungry child this weekend?

With heartfelt thanks,
Community Food Bank`
    },
    default: {
      subject: "Your Support Can Change Lives Today",
      body: `Dear Friend,

Every day, we see the impact that caring supporters like you make in our community. Your generosity transforms lives and creates hope where there was none.

The need for ${campaignGoal.toLowerCase()} has never been greater. Families in our community are counting on us to step up and make a difference.

With your donation of $35, we can take meaningful action toward ${campaignGoal.toLowerCase()}. Every dollar you give goes directly to those who need it most.

Will you join us in making a difference today?

Together, we can create positive change in our community.

With sincere appreciation,
The Team`
    }
  };

  // Determine which template to use based on keywords
  let selectedTemplate = templates.default;
  const goal = campaignGoal.toLowerCase();

  if (goal.includes('playground') || goal.includes('play') || goal.includes('park')) {
    selectedTemplate = templates.playground;
  } else if (goal.includes('school') || goal.includes('education') || goal.includes('learn') || goal.includes('student')) {
    selectedTemplate = templates.education;
  } else if (goal.includes('food') || goal.includes('meal') || goal.includes('hungry') || goal.includes('nutrition')) {
    selectedTemplate = templates.food;
  }

  return `Subject: ${selectedTemplate.subject}\n\n${selectedTemplate.body}`;
}
