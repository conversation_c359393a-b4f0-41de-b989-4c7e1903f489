import React from 'react';

const Hero: React.FC = () => {
    return (
        <section className="bg-slate-50 overflow-hidden">
            <div className="container mx-auto px-6 pt-24 pb-16 md:py-32">
                <div className="grid md:grid-cols-2 gap-12 items-center">
                    <div className="text-center md:text-left">
                        <h1 style={{ animationDelay: '0.2s', animationFillMode: 'backwards' }} className="text-4xl md:text-5xl lg:text-6xl font-extrabold text-julep-dark leading-tight animate-fadeInUp opacity-0">
                            The CRM Built For <span className="text-julep-green">Nonprofit Fundraising</span>
                        </h1>
                        <p style={{ animationDelay: '0.4s', animationFillMode: 'backwards' }} className="mt-6 text-lg md:text-xl max-w-xl mx-auto md:mx-0 text-julep-gray animate-fadeInUp opacity-0">
                            Stop fighting with your software. Julep is the all-in-one platform that helps you raise more money with less stress, so you can focus on your mission.
                        </p>
                        <div style={{ animationDelay: '0.6s', animationFillMode: 'backwards' }} className="mt-10 flex flex-col sm:flex-row justify-center md:justify-start items-center gap-4 animate-fadeInUp opacity-0">
                            <a href="#" className="w-full sm:w-auto bg-julep-green text-white font-bold px-8 py-4 rounded-lg hover:bg-opacity-90 transition-all shadow-lg text-lg">
                                Get a Free Demo
                            </a>
                            <a href="#" className="w-full sm:w-auto bg-white text-julep-dark font-bold px-8 py-4 rounded-lg hover:bg-slate-100 transition-all shadow-lg text-lg border border-slate-200">
                                Watch 2-Min Tour
                            </a>
                        </div>
                        <div style={{ animationDelay: '0.8s', animationFillMode: 'backwards' }} className="mt-8 text-sm text-slate-500 animate-fadeInUp opacity-0">
                            No credit card required.
                        </div>
                    </div>
                    <div className="relative animate-float" style={{ animationDelay: '0.5s' }}>
                         <img 
                            src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=1200&auto=format&fit=crop"
                            alt="Julep CRM dashboard showing donor analytics"
                            className="rounded-2xl shadow-2xl border-8 border-white"
                         />
                    </div>
                </div>
            </div>
        </section>
    );
}

export default Hero;