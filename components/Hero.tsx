import React from 'react';

const Hero: React.FC = () => {
    return (
        <section className="bg-slate-50 overflow-hidden">
            <div className="container mx-auto px-6 pt-20 pb-12 md:pt-24 md:pb-16">
                {/* Trust Bar */}
                <div className="text-center mb-6">
                    <div className="inline-flex items-center gap-2 bg-julep-green/10 text-julep-green px-4 py-2 rounded-full text-sm font-medium">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Trusted by 500+ nonprofits • Raised $50M+ in donations
                    </div>
                </div>

                <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center">
                    <div className="text-center md:text-left">
                        <h1 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-julep-dark leading-tight">
                            Raise 30% More Money <span className="text-julep-green">Without the Headaches</span>
                        </h1>
                        <p className="mt-4 text-lg md:text-xl max-w-xl mx-auto md:mx-0 text-julep-gray">
                            The only CRM built specifically for nonprofit fundraising. Stop juggling spreadsheets and multiple tools.
                        </p>

                        {/* Key Benefits */}
                        <div className="mt-4 space-y-2">
                            <div className="flex items-center justify-center md:justify-start gap-2 text-julep-gray text-sm">
                                <svg className="w-4 h-4 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span>Set up in under 30 minutes</span>
                            </div>
                            <div className="flex items-center justify-center md:justify-start gap-2 text-julep-gray text-sm">
                                <svg className="w-4 h-4 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span>Replace 4+ tools with one platform</span>
                            </div>
                            <div className="flex items-center justify-center md:justify-start gap-2 text-julep-gray text-sm">
                                <svg className="w-4 h-4 text-julep-green" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                                <span>Free migration & onboarding support</span>
                            </div>
                        </div>

                        <div className="mt-6 flex flex-col sm:flex-row justify-center md:justify-start items-center gap-3">
                            <a href="#" className="w-full sm:w-auto bg-julep-green text-white font-bold px-6 py-3 rounded-lg hover:bg-opacity-90 transition-all shadow-lg text-base relative overflow-hidden group">
                                <span className="relative z-10">Start Free 14-Day Trial</span>
                                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity"></div>
                            </a>
                            <a href="#" className="w-full sm:w-auto bg-white text-julep-dark font-bold px-6 py-3 rounded-lg hover:bg-slate-100 transition-all shadow-lg text-base border border-slate-200 flex items-center justify-center gap-2">
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                </svg>
                                Watch 3-Min Demo
                            </a>
                        </div>
                        <div className="mt-4 text-xs text-slate-500">
                            <div className="flex items-center justify-center md:justify-start gap-3">
                                <span>✓ No credit card required</span>
                                <span>✓ Cancel anytime</span>
                                <span>✓ Free setup</span>
                            </div>
                        </div>
                    </div>
                    <div className="relative">
                         <img
                            src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=1200&auto=format&fit=crop"
                            alt="Julep CRM dashboard showing donor analytics and fundraising metrics"
                            className="rounded-xl shadow-xl border-4 border-white w-full h-auto"
                         />
                         {/* Floating metrics */}
                         <div className="absolute -top-2 -right-2 bg-white p-3 rounded-lg shadow-lg border border-slate-100">
                            <div className="text-lg font-bold text-julep-green">+30%</div>
                            <div className="text-xs text-slate-600">Donations</div>
                         </div>
                         <div className="absolute -bottom-2 -left-2 bg-white p-3 rounded-lg shadow-lg border border-slate-100">
                            <div className="text-lg font-bold text-julep-green">5hrs</div>
                            <div className="text-xs text-slate-600">Saved/Week</div>
                         </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

export default Hero;